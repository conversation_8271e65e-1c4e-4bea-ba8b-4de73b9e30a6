{"data": {"x_axis": [{"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-******** Jio Infocomm Limited-10217"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Airtel_India_MMS-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-G5 Telekom Telekomimikasyon Limited Sirketi-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Robi Axiata Limited-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Figensoft-******** Italy"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-BUC Mobile Inc-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-Acmetel_DOM-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Texcell Messaging Limited-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-Falcon Info Systems FZE LLC-Maxis Communication BHD Malaysia"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-TaiwanMobile-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Airtel ********-10217"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Airtel Republic of Congo-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-******** Communications Corporation-1049"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-Hayo Telecom Inc-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-G5 Telekom Telekomimikasyon Limited Sirketi-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Airtel Uganda Limited-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-Acmetel_DOM-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-M800 Limited-Maxis Communication BHD Malaysia"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Airtel ********-10217"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-Bharti Airtel Ltd (EGB)-1049"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-TaiwanMobile-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS******** OU(SMS) (Onboarding)-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-******** Limited-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-******** International Carrier Services-******** Italy"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-0-Maxis Communication BHD Malaysia"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-Hayo Telecom Inc-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Javna International FZC-1049"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-M800 Limited-Maxis Communication BHD Malaysia"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-Protel S.A.L-10217"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Mr. <PERSON> (Onboarding)-Maxis Communication BHD Malaysia"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-Smart Tech Messaging PTE LTD-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-CELTEL TCHAD S.A-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-******** Jio Infocomm Limited-10217"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-GO4Mobility-Maxis Communication BHD Malaysia"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Figensoft-******** Italy"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Celtel Congo RDC Sarl-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-GCN Global Communications ******** LLC-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-Syniverse Technology-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-AVYS WHOLESALE LDA-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Bharti Airtel Lanka Pvt. Ltd-******** Italy"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Ekato ********10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-******** International Carrier Services-******** Italy"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Mr. <PERSON> (Onboarding)-Maxis Communication BHD Malaysia"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Telq 2way-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-Protel S.A.L-10217"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-GO4Mobility-Maxis Communication BHD Malaysia"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-CELTEL TCHAD S.A-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Route Mobile (Onboarding)-Maxis Communication BHD Malaysia"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Bharti Airtel Lanka Pvt. Ltd-******** Italy"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-******** Communications Corporation-1049"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-Bharti Airtel Ltd (EGB)-1049"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Javna International FZC-1049"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Voicetec Sys Ltd-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Airtel Republic of Congo-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-0-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-SMS Highway Limited-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Route Mobile (Onboarding)-Maxis Communication BHD Malaysia"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Pinnacle (Onboarding)-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Robi Axiata Limited-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-BUC Mobile Inc-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-CELTEL TCHAD S.A-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-******** Telekom AG-10217"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-MailBIT-******** Italy"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Voicetec Sys Ltd-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS******** Wholesale (Onboarding)-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM********"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Globe Teleservices Pte Limited-1049"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-JT Global (Onboarding)-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM******** Inc-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Sinch-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-SMS Highway Limited-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Airtel Uganda Limited-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-Identidad Advertising Development LLC-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-******** Limited-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Lexico Telecom Limited-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-******** Telekom AG-10217"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-GCN Global Communications ******** LLC-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-Tata Teleservices Limited-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Globe Teleservices Pte Limited-1049"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-NX Communications Pte Ltd (Onboarding)-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS******** Wholesale (Onboarding)-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Airtel Uganda Limited-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM******** Inc-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS******** OU(SMS) (Onboarding)-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Texcell Messaging Limited-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-******** Inc-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-Tata Teleservices Limited-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-IT-Decision-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Globahub AG-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-Identidad Advertising Development LLC-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Bharti Airtel Lanka Pvt. Ltd-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-MailBIT-******** Italy"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-CELTEL TCHAD S.A-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-Syniverse Technology-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Ekato ********10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-Smart Tech Messaging PTE LTD-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Route Mobile (Onboarding)-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Airtel Uganda Limited-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-0-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Telq 2way-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-CLX ******** AB-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Globahub AG-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-IT-Decision-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Pinnacle (Onboarding)-Hutchison Macau"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-AVYS WHOLESALE LDA-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-******** Inc-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Airtel_India_MMS-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Lexico Telecom Limited-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Bharti Airtel Lanka Pvt. Ltd-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo P2P_CS-Falcon Info Systems FZE LLC-Maxis Communication BHD Malaysia"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "12WE OPEW-Rv_12WEOPEW_HQ_SMPP_A2P_C_0-JT Global (Onboarding)-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-CLX ******** AB-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "NEXMO INC-Nexmo A2P_CS_PM-Celtel Congo RDC Sarl-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-NX Communications Pte Ltd (Onboarding)-10196"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "8x8 UK Limited-8x8_1_A2P_CS-Route Mobile (Onboarding)-0"}, {"Customer Name-Customer Bind-Supplier-Destination Operator": "A.A. Smartcomtech USA LLC-SmartCOMA2P-Sinch-10196"}], "y_axis": [{"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 1, "count_successful_delivery_final": -1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 1, "count_successful_delivery_final": -1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 1, "count_successful_delivery_final": -1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 1, "count_successful_delivery_final": -1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 1, "count_successful_delivery_final": -1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 1, "count_successful_delivery_final": -1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 2, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 1, "count_successful_delivery_final": -1}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 0, "sum(submission_success)": 0, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 0}, {"total_submissions": 1, "sum(submission_success)": 1, "sum(delivery_failure_count_final)": 0, "count_successful_delivery_final": 1}]}, "Y-Axis": ["total_submissions", "sum(submission_success)", "sum(delivery_failure_count_final)", "count_successful_delivery_final"]}