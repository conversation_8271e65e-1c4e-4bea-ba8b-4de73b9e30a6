{"name": "to-do-list", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.7", "@mui/lab": "^5.0.0-alpha.163", "@mui/material": "^5.15.9", "@nivo/bar": "^0.98.0", "@nivo/heatmap": "^0.98.0", "@nivo/line": "^0.98.0", "@nivo/pie": "^0.98.0", "@nivo/polar-bar": "^0.98.0", "@nivo/radial-bar": "^0.98.0", "@nivo/sankey": "^0.98.0", "@nivo/scatterplot": "^0.98.0", "@nivo/stream": "^0.98.0", "@nivo/swarmplot": "^0.98.0", "@nivo/tree": "^0.98.0", "@nivo/treemap": "^0.98.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.7", "formik": "^2.4.6", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-query": "^3.39.3", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.14"}}