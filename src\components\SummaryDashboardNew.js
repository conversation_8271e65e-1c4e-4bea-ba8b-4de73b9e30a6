import React, { useState, useEffect } from "react";
import data5 from "../utils/data5.json";

const SummaryDashboardNew = () => {
  const [summaryStats, setSummaryStats] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      const stats = calculateSummaryStats();
      setSummaryStats(stats);
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const calculateSummaryStats = () => {
    if (!data5.data || !data5.data.y_axis) {
      return {};
    }

    const totalRecords = data5.data.y_axis.length;
    const totalSubmissions = data5.data.y_axis.reduce(
      (sum, item) => sum + (item.total_submissions || 0),
      0
    );
    const totalSuccessfulSubmissions = data5.data.y_axis.reduce(
      (sum, item) => sum + (item["sum(submission_success)"] || 0),
      0
    );
    const totalDeliveryFailures = data5.data.y_axis.reduce(
      (sum, item) => sum + (item["sum(delivery_failure_count_final)"] || 0),
      0
    );
    const totalSuccessfulDeliveries = data5.data.y_axis.reduce(
      (sum, item) => sum + (item.count_successful_delivery_final || 0),
      0
    );

    const successRate = totalSubmissions > 0
      ? ((totalSuccessfulSubmissions / totalSubmissions) * 100).toFixed(2)
      : 0;

    const deliverySuccessRate = totalSubmissions > 0
      ? ((totalSuccessfulDeliveries / totalSubmissions) * 100).toFixed(2)
      : 0;

    return {
      totalRecords,
      totalSubmissions,
      totalSuccessfulSubmissions,
      totalDeliveryFailures,
      totalSuccessfulDeliveries,
      successRate,
      deliverySuccessRate,
    };
  };

  const downloadJSON = () => {
    const dataStr = JSON.stringify(data5, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = `e2e_hub_summary_report_${new Date().toISOString().split('T')[0]}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const downloadCSV = () => {
    if (!data5.data || !data5.data.x_axis || !data5.data.y_axis) {
      alert('No data available for export');
      return;
    }

    const headers = [
      'timestamp',
      'customer_bind',
      'supplier',
      'destination_operator_name',
      'total_submissions',
      'sum_submission_success',
      'sum_delivery_failure_count_final',
      'count_successful_delivery_final'
    ];

    const csvRows = [headers.join(',')];

    data5.data.x_axis.forEach((xItem, index) => {
      const xAxisKey = Object.keys(xItem)[0];
      const xAxisValue = xItem[xAxisKey];
      const yAxisData = data5.data.y_axis[index] || {};

      const parts = xAxisValue.split("-");
      const timestamp = parts.length > 0 ? parts[0] : xAxisValue;
      const customerBind = parts.length > 1 ? parts[1] : "Unknown";
      const supplier = parts.length > 2 ? parts[2] : "Unknown";
      const destination = parts.length > 3 ? parts[3] : "Unknown";

      const row = [
        `"${timestamp}"`,
        `"${customerBind}"`,
        `"${supplier}"`,
        `"${destination}"`,
        yAxisData.total_submissions || 0,
        yAxisData["sum(submission_success)"] || 0,
        yAxisData["sum(delivery_failure_count_final)"] || 0,
        yAxisData.count_successful_delivery_final || 0
      ];

      csvRows.push(row.join(','));
    });

    const csvString = csvRows.join('\n');
    const blob = new Blob([csvString], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);

    const exportFileDefaultName = `e2e_hub_summary_report_${new Date().toISOString().split('T')[0]}.csv`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', url);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    window.URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-xl p-8 flex items-center space-x-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-gray-600 text-lg">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Modern Header */}
      <div className="bg-white shadow-lg border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-4 mb-2">
                <div className="w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                    E2E Hub Analytics
                  </h1>
                  <p className="text-gray-600 text-lg">
                    Comprehensive telecommunications data insights & reporting
                  </p>
                </div>
              </div>
            </div>
            <div className="hidden lg:flex items-center space-x-6">
              <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200 shadow-sm">
                <div className="text-sm text-gray-500 mb-1">Total Records</div>
                <div className="text-3xl font-bold text-gray-900">{summaryStats.totalRecords}</div>
              </div>
              <div className="text-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl border border-green-200 shadow-sm">
                <div className="text-sm text-gray-500 mb-1">Success Rate</div>
                <div className="text-3xl font-bold text-green-600">{summaryStats.successRate}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8 space-y-8">
        {/* Key Metrics Dashboard */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-shadow duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Total Submissions</p>
                <p className="text-3xl font-bold text-gray-900">{summaryStats.totalSubmissions}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-shadow duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Successful Submissions</p>
                <p className="text-3xl font-bold text-green-600">{summaryStats.totalSuccessfulSubmissions}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-shadow duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Delivery Failures</p>
                <p className="text-3xl font-bold text-red-600">{summaryStats.totalDeliveryFailures}</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-shadow duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 mb-1">Delivery Success Rate</p>
                <p className="text-3xl font-bold text-purple-600">{summaryStats.deliverySuccessRate}%</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Applied Filters Card */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h3 className="text-xl font-bold text-white flex items-center">
              <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
              </svg>
              Applied Filters & Query Configuration
            </h3>
          </div>

          <div className="p-6 space-y-6">
            {/* Time Range */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
              <h4 className="font-semibold text-blue-800 mb-3 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Time Range Configuration
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white rounded-lg p-3 border border-blue-100">
                  <span className="text-xs text-gray-500 uppercase tracking-wide">Start Time</span>
                  <div className="font-medium text-gray-900">{data5.query_info.timestamp_range.start}</div>
                </div>
                <div className="bg-white rounded-lg p-3 border border-blue-100">
                  <span className="text-xs text-gray-500 uppercase tracking-wide">End Time</span>
                  <div className="font-medium text-gray-900">{data5.query_info.timestamp_range.end}</div>
                </div>
                <div className="bg-white rounded-lg p-3 border border-blue-100">
                  <span className="text-xs text-gray-500 uppercase tracking-wide">Timezone</span>
                  <div className="font-medium text-gray-900">{data5.query_info.timestamp_range.timezone}</div>
                </div>
              </div>
            </div>

            {/* Customer Names */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
              <h4 className="font-semibold text-green-800 mb-3 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Customer Names ({data5.query_info.filters.customer_names.length})
              </h4>
              <div className="flex flex-wrap gap-2">
                {data5.query_info.filters.customer_names.map((customer, index) => (
                  <span
                    key={index}
                    className="px-4 py-2 bg-white text-green-800 rounded-full text-sm font-medium border border-green-200 shadow-sm"
                  >
                    {customer}
                  </span>
                ))}
              </div>
            </div>

            {/* Customer Binds */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-200">
              <h4 className="font-semibold text-purple-800 mb-3 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
                Customer Binds ({data5.query_info.filters.customer_binds.length})
              </h4>
              <div className="flex flex-wrap gap-2">
                {data5.query_info.filters.customer_binds.map((bind, index) => (
                  <span
                    key={index}
                    className="px-4 py-2 bg-white text-purple-800 rounded-full text-sm font-medium border border-purple-200 shadow-sm"
                  >
                    {bind}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Data Schema Card */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
            <h3 className="text-xl font-bold text-white flex items-center">
              <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
              </svg>
              E2E Hub Data Schema
            </h3>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* X-Axis Fields */}
              <div>
                <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                  X-Axis Fields (38 Dimensions)
                </h4>
                <div className="bg-blue-50 rounded-xl p-4 border border-blue-200 max-h-64 overflow-y-auto">
                  <div className="grid grid-cols-1 gap-2">
                    {[
                      'timestamp', 'customer_name', 'customer_bind', 'status', 'supplier', 'supplier_bind',
                      'destination_country_name', 'destination_operator_name', 'source_operator_code',
                      'source_operator_name', 'destination_operator_code', 'lcr_name', 'source_mcc',
                      'source_mnc', 'source_country_code', 'source_country_name', 'source_protocol',
                      'visiting_operator', 'visiting_operator_id', 'destination_protocol',
                      'customer_interconnect', 'supplier_interconnect', 'src_hub', 'dest_hub',
                      'supplier_system_id', 'customer_system_id', 'spec_lcr', 'customer_kam',
                      'supplier_kam', 'source_mnp_supplier', 'destination_mnp_supplier',
                      'final_operator_name', 'destination_mnc_final', 'destination_mcc_final',
                      'supplier_billing_logic', 'customer_billing_logic', 'traffic_type_customer',
                      'traffic_type_supplier'
                    ].map((field, index) => (
                      <div key={index} className="text-sm text-blue-800 bg-white px-3 py-2 rounded-lg border border-blue-100 font-mono">
                        {field}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Y-Axis Fields */}
              <div>
                <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  Y-Axis Fields (12 Metrics)
                </h4>
                <div className="bg-green-50 rounded-xl p-4 border border-green-200">
                  <div className="grid grid-cols-1 gap-2">
                    {[
                      'total_submissions', 'submission_success', 'submission_efficiency', 'total_deliveries',
                      'next_hop_success_new', 'next_hop_success_percent_final', 'delivery_failure_count_final',
                      'count_successful_delivery_final', 'final_delivery_efficiency', 'percentage_failure',
                      'percentage_successful', 'submission_error'
                    ].map((field, index) => (
                      <div key={index} className="text-sm text-green-800 bg-white px-3 py-2 rounded-lg border border-green-100 font-mono">
                        {field}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Download Actions */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
          <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <svg className="w-6 h-6 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export Data
          </h3>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={downloadJSON}
              className="px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download JSON
              <span className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded">Complete Data</span>
            </button>
            <button
              onClick={downloadCSV}
              className="px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download CSV
              <span className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded">Spreadsheet Ready</span>
            </button>
          </div>
        </div>

        {/* Uniqueness Note */}
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl border border-yellow-200 p-6">
          <h4 className="font-semibold text-yellow-800 mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Data Uniqueness & Time Range Filtering
          </h4>
          <p className="text-yellow-700 leading-relaxed">
            When a time range filter is applied, at least one additional x-axis component (like customer_bind or supplier)
            is required along with timestamp to avoid duplicates within that time range. This ensures data integrity while
            allowing multiple records per timestamp for different customer-supplier-destination combinations.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SummaryDashboardNew;
